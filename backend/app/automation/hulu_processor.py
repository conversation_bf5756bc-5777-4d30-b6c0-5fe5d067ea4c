"""
Hulu自动化处理器 - 负责Hulu账号和支付卡的自动化验证
"""
import asyncio
import logging
from typing import Optional, Dict, Any
from playwright.async_api import Page, BrowserContext
from ..models.account import HuluAccount, PaymentCard, AccountCardPair, AccountStatus, PaymentCardStatus
from .browser_manager import browser_manager

logger = logging.getLogger(__name__)


class HuluProcessor:
    """Hulu自动化处理器"""
    
    def __init__(self):
        self.hulu_login_url = "https://auth.hulu.com/web/login"
        self.hulu_account_url = "https://secure.hulu.com/account"
        self.hulu_payment_url = "https://secure.hulu.com/account/payment"
    
    async def process_account_card_pair(
        self, 
        account: HuluAccount, 
        card: PaymentCard,
        context: Optional[BrowserContext] = None
    ) -> AccountCardPair:
        """处理账号-支付卡配对"""
        pair_id = f"{account.email}_{card.get_last_4_digits()}"
        pair = AccountCardPair(
            account=account,
            card=card,
            pair_id=pair_id,
            status=AccountStatus.PROCESSING
        )
        
        page = None
        try:
            # 创建页面
            if not context:
                context = await browser_manager.create_context()
            page = await browser_manager.create_page(context)
            
            # 步骤1: 登录Hulu账号
            login_result = await self._login_hulu_account(page, account)
            if not login_result['success']:
                pair.status = AccountStatus.FAILED
                pair.error_message = f"登录失败: {login_result['error']}"
                account.status = AccountStatus.FAILED
                account.error_message = pair.error_message
                return pair
            
            # 步骤2: 验证支付卡
            payment_result = await self._validate_payment_card(page, card)
            if not payment_result['success']:
                pair.status = AccountStatus.FAILED
                pair.error_message = f"支付卡验证失败: {payment_result['error']}"
                card.status = PaymentCardStatus.INVALID
                return pair
            
            # 步骤3: 更新状态
            pair.status = AccountStatus.SUCCESS
            pair.validation_result = {
                'login_success': True,
                'payment_validation': payment_result,
                'card_last_4': card.get_last_4_digits()
            }
            
            account.status = AccountStatus.SUCCESS
            card.status = PaymentCardStatus.VALID
            card.last_4_digits = card.get_last_4_digits()
            
            logger.info(f"成功处理账号-支付卡配对: {pair_id}")
            
        except Exception as e:
            logger.error(f"处理账号-支付卡配对失败 {pair_id}: {e}")
            pair.status = AccountStatus.FAILED
            pair.error_message = str(e)
            account.status = AccountStatus.FAILED
            account.error_message = str(e)
        
        finally:
            if page:
                await page.close()
        
        return pair
    
    async def _login_hulu_account(self, page: Page, account: HuluAccount) -> Dict[str, Any]:
        """登录Hulu账号"""
        try:
            logger.info(f"开始登录Hulu账号: {account.email}")
            
            # 访问登录页面
            await page.goto(self.hulu_login_url, wait_until='networkidle')
            await browser_manager.random_delay(2, 4)
            
            # 检查是否已经登录
            if "secure.hulu.com" in page.url:
                logger.info(f"账号 {account.email} 已经登录")
                return {'success': True, 'message': 'Already logged in'}
            
            # 输入邮箱
            email_selector = 'input[name="email"], input[type="email"], #email'
            await browser_manager.human_like_type(page, email_selector, account.email)
            await browser_manager.random_delay(1, 2)
            
            # 输入密码
            password_selector = 'input[name="password"], input[type="password"], #password'
            await browser_manager.human_like_type(page, password_selector, account.password)
            await browser_manager.random_delay(1, 2)
            
            # 点击登录按钮
            login_button_selector = 'button[type="submit"], .login-button, #login-button'
            await browser_manager.human_like_click(page, login_button_selector)
            
            # 等待登录结果
            await page.wait_for_load_state('networkidle', timeout=15000)
            await browser_manager.random_delay(2, 4)
            
            # 检查登录是否成功
            current_url = page.url
            if "secure.hulu.com" in current_url or "account" in current_url:
                logger.info(f"账号 {account.email} 登录成功")
                return {'success': True, 'message': 'Login successful'}
            
            # 检查是否有错误信息
            error_selectors = [
                '.error-message', '.alert-error', '.login-error',
                '[data-testid="error"]', '.notification--error'
            ]
            
            for selector in error_selectors:
                try:
                    error_element = await page.wait_for_selector(selector, timeout=2000)
                    if error_element:
                        error_text = await error_element.text_content()
                        logger.warning(f"登录错误信息: {error_text}")
                        return {'success': False, 'error': error_text}
                except:
                    continue
            
            return {'success': False, 'error': 'Login failed - unknown reason'}
            
        except Exception as e:
            logger.error(f"登录过程中发生异常: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _validate_payment_card(self, page: Page, card: PaymentCard) -> Dict[str, Any]:
        """验证支付卡"""
        try:
            logger.info(f"开始验证支付卡: ****{card.get_last_4_digits()}")
            
            # 导航到支付页面
            await page.goto(self.hulu_payment_url, wait_until='networkidle')
            await browser_manager.random_delay(2, 4)
            
            # 查找添加支付方式按钮
            add_payment_selectors = [
                '.add-payment-method', '.add-card-button', 
                '[data-testid="add-payment"]', 'button:has-text("Add")'
            ]
            
            add_button = None
            for selector in add_payment_selectors:
                try:
                    add_button = await page.wait_for_selector(selector, timeout=3000)
                    if add_button:
                        break
                except:
                    continue
            
            if add_button:
                await browser_manager.human_like_click(page, add_payment_selectors[0])
                await browser_manager.random_delay(1, 2)
            
            # 填写支付卡信息
            card_fields = {
                'card_number': ['input[name="cardNumber"]', 'input[placeholder*="card number"]', '#card-number'],
                'expiry_month': ['select[name="expiryMonth"]', 'input[name="expMonth"]', '#exp-month'],
                'expiry_year': ['select[name="expiryYear"]', 'input[name="expYear"]', '#exp-year'],
                'cvv': ['input[name="cvv"]', 'input[name="securityCode"]', '#cvv'],
                'cardholder_name': ['input[name="cardholderName"]', 'input[name="nameOnCard"]', '#cardholder-name']
            }
            
            # 填写卡号
            for selector in card_fields['card_number']:
                try:
                    await browser_manager.human_like_type(page, selector, card.card_number)
                    break
                except:
                    continue
            
            await browser_manager.random_delay(0.5, 1)
            
            # 填写过期月份
            for selector in card_fields['expiry_month']:
                try:
                    if 'select' in selector:
                        await page.select_option(selector, card.expiry_month)
                    else:
                        await browser_manager.human_like_type(page, selector, card.expiry_month)
                    break
                except:
                    continue
            
            await browser_manager.random_delay(0.5, 1)
            
            # 填写过期年份
            for selector in card_fields['expiry_year']:
                try:
                    if 'select' in selector:
                        await page.select_option(selector, card.expiry_year)
                    else:
                        await browser_manager.human_like_type(page, selector, card.expiry_year)
                    break
                except:
                    continue
            
            await browser_manager.random_delay(0.5, 1)
            
            # 填写CVV
            for selector in card_fields['cvv']:
                try:
                    await browser_manager.human_like_type(page, selector, card.cvv)
                    break
                except:
                    continue
            
            await browser_manager.random_delay(0.5, 1)
            
            # 填写持卡人姓名
            for selector in card_fields['cardholder_name']:
                try:
                    await browser_manager.human_like_type(page, selector, card.cardholder_name)
                    break
                except:
                    continue
            
            await browser_manager.random_delay(1, 2)
            
            # 提交支付信息
            submit_selectors = [
                'button[type="submit"]', '.submit-button', '.save-button',
                'button:has-text("Save")', 'button:has-text("Add")'
            ]
            
            for selector in submit_selectors:
                try:
                    await browser_manager.human_like_click(page, selector)
                    break
                except:
                    continue
            
            # 等待处理结果
            await page.wait_for_load_state('networkidle', timeout=15000)
            await browser_manager.random_delay(2, 4)
            
            # 检查是否成功添加
            success_indicators = [
                '.success-message', '.payment-added', '.card-added',
                '[data-testid="success"]', '.notification--success'
            ]
            
            for selector in success_indicators:
                try:
                    success_element = await page.wait_for_selector(selector, timeout=3000)
                    if success_element:
                        logger.info(f"支付卡验证成功: ****{card.get_last_4_digits()}")
                        return {
                            'success': True, 
                            'message': 'Payment card validated successfully',
                            'last_4_digits': card.get_last_4_digits()
                        }
                except:
                    continue
            
            # 检查错误信息
            error_selectors = [
                '.error-message', '.payment-error', '.card-error',
                '[data-testid="error"]', '.notification--error'
            ]
            
            for selector in error_selectors:
                try:
                    error_element = await page.wait_for_selector(selector, timeout=2000)
                    if error_element:
                        error_text = await error_element.text_content()
                        logger.warning(f"支付卡验证错误: {error_text}")
                        return {'success': False, 'error': error_text}
                except:
                    continue
            
            # 如果没有明确的成功或失败指示，检查页面URL变化
            current_url = page.url
            if "payment" in current_url and "error" not in current_url:
                return {
                    'success': True, 
                    'message': 'Payment card likely validated (no error detected)',
                    'last_4_digits': card.get_last_4_digits()
                }
            
            return {'success': False, 'error': 'Payment validation failed - unknown reason'}
            
        except Exception as e:
            logger.error(f"支付卡验证过程中发生异常: {e}")
            return {'success': False, 'error': str(e)}
