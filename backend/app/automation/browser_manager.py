"""
浏览器管理器 - 负责浏览器实例的创建、管理和防检测配置
"""
import asyncio
import random
import logging
from typing import Optional, Dict, Any, List
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
from ..core.config import settings

logger = logging.getLogger(__name__)


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.contexts: List[BrowserContext] = []
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
        ]
    
    async def start(self):
        """启动浏览器"""
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=not settings.debug,
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-web-security',
                    '--disable-features=TranslateUI',
                    '--disable-ipc-flooding-protection',
                    '--no-first-run',
                    '--no-default-browser-check',
                ]
            )
            logger.info("浏览器启动成功")
        except Exception as e:
            logger.error(f"浏览器启动失败: {e}")
            raise
    
    async def create_context(self, proxy: Optional[Dict[str, str]] = None) -> BrowserContext:
        """创建新的浏览器上下文"""
        if not self.browser:
            await self.start()
        
        # 随机选择User-Agent
        user_agent = random.choice(self.user_agents)
        
        # 创建上下文配置
        context_options = {
            'user_agent': user_agent,
            'viewport': {'width': 1920, 'height': 1080},
            'locale': 'en-US',
            'timezone_id': 'America/New_York',
            'permissions': ['geolocation'],
            'geolocation': {'latitude': 40.7128, 'longitude': -74.0060},  # New York
            'extra_http_headers': {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        }
        
        # 添加代理配置
        if proxy:
            context_options['proxy'] = proxy
        
        context = await self.browser.new_context(**context_options)
        
        # 注入反检测脚本
        await context.add_init_script("""
            // 移除webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 修改plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 修改languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            // 修改chrome属性
            window.chrome = {
                runtime: {},
            };
            
            // 修改permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        self.contexts.append(context)
        logger.info(f"创建新的浏览器上下文，当前上下文数量: {len(self.contexts)}")
        return context
    
    async def create_page(self, context: Optional[BrowserContext] = None) -> Page:
        """创建新页面"""
        if not context:
            context = await self.create_context()
        
        page = await context.new_page()
        
        # 设置页面超时
        page.set_default_timeout(30000)
        page.set_default_navigation_timeout(30000)
        
        return page
    
    async def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """随机延迟"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def human_like_type(self, page: Page, selector: str, text: str, delay_range: tuple = (50, 150)):
        """模拟人类打字"""
        element = await page.wait_for_selector(selector)
        await element.click()
        await self.random_delay(0.1, 0.3)
        
        for char in text:
            await element.type(char)
            delay = random.randint(delay_range[0], delay_range[1])
            await asyncio.sleep(delay / 1000)
    
    async def human_like_click(self, page: Page, selector: str):
        """模拟人类点击"""
        element = await page.wait_for_selector(selector)
        
        # 获取元素位置
        box = await element.bounding_box()
        if box:
            # 在元素范围内随机点击
            x = box['x'] + random.uniform(0.2, 0.8) * box['width']
            y = box['y'] + random.uniform(0.2, 0.8) * box['height']
            
            # 移动鼠标到目标位置
            await page.mouse.move(x, y)
            await self.random_delay(0.1, 0.3)
            
            # 点击
            await page.mouse.click(x, y)
        else:
            await element.click()
    
    async def close_context(self, context: BrowserContext):
        """关闭浏览器上下文"""
        try:
            await context.close()
            if context in self.contexts:
                self.contexts.remove(context)
            logger.info(f"关闭浏览器上下文，剩余上下文数量: {len(self.contexts)}")
        except Exception as e:
            logger.error(f"关闭浏览器上下文失败: {e}")
    
    async def close_all(self):
        """关闭所有浏览器资源"""
        try:
            # 关闭所有上下文
            for context in self.contexts.copy():
                await self.close_context(context)
            
            # 关闭浏览器
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            # 停止playwright
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            logger.info("所有浏览器资源已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器资源失败: {e}")


# 全局浏览器管理器实例
browser_manager = BrowserManager()
