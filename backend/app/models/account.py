"""
Hulu账号数据模型
"""
from datetime import datetime
from enum import Enum
from typing import Optional, List
from pydantic import BaseModel, Field, validator


class AccountStatus(str, Enum):
    """账号状态枚举"""
    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    SUCCESS = "success"  # 成功
    FAILED = "failed"  # 失败
    INVALID = "invalid"  # 无效


class PaymentCardStatus(str, Enum):
    """支付卡状态枚举"""
    VALID = "valid"  # 有效
    INVALID = "invalid"  # 无效
    EXPIRED = "expired"  # 过期
    DECLINED = "declined"  # 被拒绝
    UNKNOWN = "unknown"  # 未知


class HuluAccount(BaseModel):
    """Hulu账号模型"""
    email: str = Field(..., description="账号邮箱")
    password: str = Field(..., description="账号密码")
    status: AccountStatus = Field(default=AccountStatus.PENDING, description="账号状态")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    last_login: Optional[datetime] = Field(default=None, description="最后登录时间")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValueError('Invalid email format')
        return v.lower()


class PaymentCard(BaseModel):
    """支付卡模型"""
    card_number: str = Field(..., description="卡号")
    expiry_month: str = Field(..., description="过期月份")
    expiry_year: str = Field(..., description="过期年份")
    cvv: str = Field(..., description="CVV")
    cardholder_name: str = Field(..., description="持卡人姓名")
    billing_address: Optional[str] = Field(default=None, description="账单地址")
    status: PaymentCardStatus = Field(default=PaymentCardStatus.UNKNOWN, description="卡片状态")
    last_4_digits: Optional[str] = Field(default=None, description="卡号后4位")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    
    @validator('card_number')
    def validate_card_number(cls, v):
        # 移除空格和连字符
        cleaned = v.replace(' ', '').replace('-', '')
        if not cleaned.isdigit() or len(cleaned) < 13 or len(cleaned) > 19:
            raise ValueError('Invalid card number format')
        return cleaned
    
    @validator('cvv')
    def validate_cvv(cls, v):
        if not v.isdigit() or len(v) < 3 or len(v) > 4:
            raise ValueError('Invalid CVV format')
        return v
    
    def get_last_4_digits(self) -> str:
        """获取卡号后4位"""
        return self.card_number[-4:] if len(self.card_number) >= 4 else self.card_number


class AccountCardPair(BaseModel):
    """账号-支付卡配对模型"""
    account: HuluAccount
    card: PaymentCard
    pair_id: str = Field(..., description="配对ID")
    status: AccountStatus = Field(default=AccountStatus.PENDING, description="配对状态")
    validation_result: Optional[dict] = Field(default=None, description="验证结果")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: Optional[datetime] = Field(default=None, description="更新时间")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class BatchProcessRequest(BaseModel):
    """批量处理请求模型"""
    accounts: List[HuluAccount] = Field(..., description="账号列表")
    cards: List[PaymentCard] = Field(..., description="支付卡列表")
    batch_id: str = Field(..., description="批次ID")
    max_concurrent: int = Field(default=3, description="最大并发数")
    retry_failed: bool = Field(default=True, description="是否重试失败项")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")


class BatchProcessResult(BaseModel):
    """批量处理结果模型"""
    batch_id: str = Field(..., description="批次ID")
    total_count: int = Field(..., description="总数量")
    success_count: int = Field(default=0, description="成功数量")
    failed_count: int = Field(default=0, description="失败数量")
    pending_count: int = Field(default=0, description="待处理数量")
    results: List[AccountCardPair] = Field(default=[], description="处理结果")
    started_at: datetime = Field(default_factory=datetime.now, description="开始时间")
    completed_at: Optional[datetime] = Field(default=None, description="完成时间")
    status: str = Field(default="running", description="批次状态")
