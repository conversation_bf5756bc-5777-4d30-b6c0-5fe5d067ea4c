"""
数据处理服务 - 负责数据的导入、验证、清洗和输出
"""
import csv
import json
import logging
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from ..models.account import HuluAccount, PaymentCard, BatchProcessRequest, AccountStatus, PaymentCardStatus

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        self.input_dir = Path("data/input")
        self.output_dir = Path("data/output")
        self.temp_dir = Path("data/temp")
        
        # 确保目录存在
        for dir_path in [self.input_dir, self.output_dir, self.temp_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def load_accounts_from_csv(self, file_path: str) -> List[HuluAccount]:
        """从CSV文件加载Hulu账号"""
        accounts = []
        try:
            df = pd.read_csv(file_path)
            
            # 验证必需的列
            required_columns = ['email', 'password']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")
            
            for _, row in df.iterrows():
                try:
                    account = HuluAccount(
                        email=str(row['email']).strip(),
                        password=str(row['password']).strip()
                    )
                    accounts.append(account)
                except Exception as e:
                    logger.warning(f"跳过无效的账号行: {row.to_dict()}, 错误: {e}")
            
            logger.info(f"从 {file_path} 成功加载 {len(accounts)} 个账号")
            
        except Exception as e:
            logger.error(f"加载账号文件失败 {file_path}: {e}")
            raise
        
        return accounts
    
    def load_cards_from_csv(self, file_path: str) -> List[PaymentCard]:
        """从CSV文件加载支付卡"""
        cards = []
        try:
            df = pd.read_csv(file_path)
            
            # 验证必需的列
            required_columns = ['card_number', 'expiry_month', 'expiry_year', 'cvv', 'cardholder_name']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")
            
            for _, row in df.iterrows():
                try:
                    card = PaymentCard(
                        card_number=str(row['card_number']).strip(),
                        expiry_month=str(row['expiry_month']).strip().zfill(2),
                        expiry_year=str(row['expiry_year']).strip(),
                        cvv=str(row['cvv']).strip(),
                        cardholder_name=str(row['cardholder_name']).strip(),
                        billing_address=str(row.get('billing_address', '')).strip() or None
                    )
                    cards.append(card)
                except Exception as e:
                    logger.warning(f"跳过无效的支付卡行: {row.to_dict()}, 错误: {e}")
            
            logger.info(f"从 {file_path} 成功加载 {len(cards)} 张支付卡")
            
        except Exception as e:
            logger.error(f"加载支付卡文件失败 {file_path}: {e}")
            raise
        
        return cards
    
    def load_accounts_from_json(self, file_path: str) -> List[HuluAccount]:
        """从JSON文件加载Hulu账号"""
        accounts = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                account_list = data
            elif isinstance(data, dict) and 'accounts' in data:
                account_list = data['accounts']
            else:
                raise ValueError("JSON格式不正确，应该是账号列表或包含'accounts'键的对象")
            
            for item in account_list:
                try:
                    account = HuluAccount(**item)
                    accounts.append(account)
                except Exception as e:
                    logger.warning(f"跳过无效的账号: {item}, 错误: {e}")
            
            logger.info(f"从 {file_path} 成功加载 {len(accounts)} 个账号")
            
        except Exception as e:
            logger.error(f"加载账号文件失败 {file_path}: {e}")
            raise
        
        return accounts
    
    def load_cards_from_json(self, file_path: str) -> List[PaymentCard]:
        """从JSON文件加载支付卡"""
        cards = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if isinstance(data, list):
                card_list = data
            elif isinstance(data, dict) and 'cards' in data:
                card_list = data['cards']
            else:
                raise ValueError("JSON格式不正确，应该是支付卡列表或包含'cards'键的对象")
            
            for item in card_list:
                try:
                    card = PaymentCard(**item)
                    cards.append(card)
                except Exception as e:
                    logger.warning(f"跳过无效的支付卡: {item}, 错误: {e}")
            
            logger.info(f"从 {file_path} 成功加载 {len(cards)} 张支付卡")
            
        except Exception as e:
            logger.error(f"加载支付卡文件失败 {file_path}: {e}")
            raise
        
        return cards
    
    def validate_data(self, accounts: List[HuluAccount], cards: List[PaymentCard]) -> Dict[str, Any]:
        """验证数据完整性"""
        validation_result = {
            'valid': True,
            'accounts': {
                'total': len(accounts),
                'valid': 0,
                'invalid': 0,
                'errors': []
            },
            'cards': {
                'total': len(cards),
                'valid': 0,
                'invalid': 0,
                'errors': []
            }
        }
        
        # 验证账号
        for i, account in enumerate(accounts):
            try:
                # 重新验证以确保数据正确
                HuluAccount(**account.dict())
                validation_result['accounts']['valid'] += 1
            except Exception as e:
                validation_result['accounts']['invalid'] += 1
                validation_result['accounts']['errors'].append(f"账号 {i+1}: {e}")
                validation_result['valid'] = False
        
        # 验证支付卡
        for i, card in enumerate(cards):
            try:
                # 重新验证以确保数据正确
                PaymentCard(**card.dict())
                validation_result['cards']['valid'] += 1
            except Exception as e:
                validation_result['cards']['invalid'] += 1
                validation_result['cards']['errors'].append(f"支付卡 {i+1}: {e}")
                validation_result['valid'] = False
        
        logger.info(f"数据验证完成: 账号 {validation_result['accounts']['valid']}/{validation_result['accounts']['total']}, "
                   f"支付卡 {validation_result['cards']['valid']}/{validation_result['cards']['total']}")
        
        return validation_result
    
    def create_batch_request(self, accounts: List[HuluAccount], cards: List[PaymentCard], 
                           batch_id: Optional[str] = None, max_concurrent: int = 3) -> BatchProcessRequest:
        """创建批量处理请求"""
        if not batch_id:
            batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        return BatchProcessRequest(
            accounts=accounts,
            cards=cards,
            batch_id=batch_id,
            max_concurrent=max_concurrent
        )
    
    def export_results_to_csv(self, results: List[Any], file_path: str, result_type: str = "pairs"):
        """导出结果到CSV文件"""
        try:
            if result_type == "pairs":
                # 导出账号-支付卡配对结果
                data = []
                for pair in results:
                    data.append({
                        'pair_id': pair.pair_id,
                        'email': pair.account.email,
                        'card_last_4': pair.card.get_last_4_digits(),
                        'status': pair.status,
                        'error_message': pair.error_message or '',
                        'created_at': pair.created_at.isoformat(),
                        'updated_at': pair.updated_at.isoformat() if pair.updated_at else ''
                    })
            elif result_type == "accounts":
                # 导出账号结果
                data = []
                for account in results:
                    data.append({
                        'email': account.email,
                        'status': account.status,
                        'error_message': account.error_message or '',
                        'created_at': account.created_at.isoformat(),
                        'updated_at': account.updated_at.isoformat() if account.updated_at else '',
                        'last_login': account.last_login.isoformat() if account.last_login else ''
                    })
            elif result_type == "cards":
                # 导出支付卡结果
                data = []
                for card in results:
                    data.append({
                        'last_4_digits': card.get_last_4_digits(),
                        'cardholder_name': card.cardholder_name,
                        'status': card.status,
                        'created_at': card.created_at.isoformat(),
                        'updated_at': card.updated_at.isoformat() if card.updated_at else ''
                    })
            else:
                raise ValueError(f"不支持的结果类型: {result_type}")
            
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False, encoding='utf-8')
            logger.info(f"结果已导出到 {file_path}")
            
        except Exception as e:
            logger.error(f"导出结果失败 {file_path}: {e}")
            raise
    
    def export_results_to_json(self, results: List[Any], file_path: str):
        """导出结果到JSON文件"""
        try:
            # 转换为可序列化的格式
            data = []
            for item in results:
                if hasattr(item, 'dict'):
                    data.append(item.dict())
                else:
                    data.append(item)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            logger.info(f"结果已导出到 {file_path}")
            
        except Exception as e:
            logger.error(f"导出结果失败 {file_path}: {e}")
            raise
    
    def generate_summary_report(self, results: List[Any], batch_id: str) -> Dict[str, Any]:
        """生成汇总报告"""
        if not results:
            return {
                'batch_id': batch_id,
                'total_count': 0,
                'summary': 'No results to process'
            }
        
        # 统计结果
        status_counts = {}
        for item in results:
            status = getattr(item, 'status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1
        
        # 计算成功率
        total_count = len(results)
        success_count = status_counts.get(AccountStatus.SUCCESS, 0)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        report = {
            'batch_id': batch_id,
            'total_count': total_count,
            'success_count': success_count,
            'failed_count': status_counts.get(AccountStatus.FAILED, 0),
            'pending_count': status_counts.get(AccountStatus.PENDING, 0),
            'success_rate': round(success_rate, 2),
            'status_breakdown': status_counts,
            'generated_at': datetime.now().isoformat()
        }
        
        return report


# 全局数据处理器实例
data_processor = DataProcessor()
