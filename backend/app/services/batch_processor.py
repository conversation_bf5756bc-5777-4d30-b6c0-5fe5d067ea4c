"""
批量处理服务 - 负责批量处理账号和支付卡的验证
"""
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from ..models.account import (
    HuluAccount, PaymentCard, AccountCardPair, BatchProcessRequest, 
    BatchProcessResult, AccountStatus
)
from ..automation.hulu_processor import HuluProcessor
from ..automation.browser_manager import browser_manager

logger = logging.getLogger(__name__)


class BatchProcessor:
    """批量处理器"""
    
    def __init__(self):
        self.hulu_processor = HuluProcessor()
        self.active_batches: Dict[str, BatchProcessResult] = {}
        self.semaphore_pool: Dict[str, asyncio.Semaphore] = {}
    
    async def process_batch(self, request: BatchProcessRequest) -> BatchProcessResult:
        """处理批量请求"""
        logger.info(f"开始处理批次: {request.batch_id}, 账号数: {len(request.accounts)}, 支付卡数: {len(request.cards)}")
        
        # 创建批次结果
        result = BatchProcessResult(
            batch_id=request.batch_id,
            total_count=min(len(request.accounts), len(request.cards)),
            started_at=datetime.now()
        )
        
        # 注册活跃批次
        self.active_batches[request.batch_id] = result
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(request.max_concurrent)
        self.semaphore_pool[request.batch_id] = semaphore
        
        try:
            # 创建账号-支付卡配对
            pairs = self._create_account_card_pairs(request.accounts, request.cards)
            result.total_count = len(pairs)
            
            # 并发处理配对
            tasks = []
            for pair in pairs:
                task = self._process_pair_with_semaphore(
                    semaphore, pair.account, pair.card, request.batch_id
                )
                tasks.append(task)
            
            # 等待所有任务完成
            completed_pairs = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for pair_result in completed_pairs:
                if isinstance(pair_result, Exception):
                    logger.error(f"处理配对时发生异常: {pair_result}")
                    # 创建失败的配对结果
                    failed_pair = AccountCardPair(
                        account=HuluAccount(email="unknown", password="unknown"),
                        card=PaymentCard(
                            card_number="0000", expiry_month="01", expiry_year="2025",
                            cvv="000", cardholder_name="unknown"
                        ),
                        pair_id="failed",
                        status=AccountStatus.FAILED,
                        error_message=str(pair_result)
                    )
                    result.results.append(failed_pair)
                    result.failed_count += 1
                else:
                    result.results.append(pair_result)
                    if pair_result.status == AccountStatus.SUCCESS:
                        result.success_count += 1
                    elif pair_result.status == AccountStatus.FAILED:
                        result.failed_count += 1
                    else:
                        result.pending_count += 1
            
            result.completed_at = datetime.now()
            result.status = "completed"
            
            logger.info(f"批次处理完成: {request.batch_id}, "
                       f"成功: {result.success_count}, 失败: {result.failed_count}")
            
        except Exception as e:
            logger.error(f"批次处理失败 {request.batch_id}: {e}")
            result.status = "failed"
            result.completed_at = datetime.now()
            raise
        
        finally:
            # 清理资源
            if request.batch_id in self.semaphore_pool:
                del self.semaphore_pool[request.batch_id]
        
        return result
    
    def _create_account_card_pairs(self, accounts: List[HuluAccount], cards: List[PaymentCard]) -> List[AccountCardPair]:
        """创建账号-支付卡配对"""
        pairs = []
        
        # 简单的一对一配对策略
        min_count = min(len(accounts), len(cards))
        
        for i in range(min_count):
            account = accounts[i]
            card = cards[i]
            pair_id = f"{account.email}_{card.get_last_4_digits()}"
            
            pair = AccountCardPair(
                account=account,
                card=card,
                pair_id=pair_id,
                status=AccountStatus.PENDING
            )
            pairs.append(pair)
        
        logger.info(f"创建了 {len(pairs)} 个账号-支付卡配对")
        return pairs
    
    async def _process_pair_with_semaphore(
        self, 
        semaphore: asyncio.Semaphore, 
        account: HuluAccount, 
        card: PaymentCard,
        batch_id: str
    ) -> AccountCardPair:
        """使用信号量控制的配对处理"""
        async with semaphore:
            try:
                # 创建独立的浏览器上下文
                context = await browser_manager.create_context()
                
                # 处理配对
                result = await self.hulu_processor.process_account_card_pair(
                    account, card, context
                )
                
                # 更新批次状态
                if batch_id in self.active_batches:
                    batch_result = self.active_batches[batch_id]
                    if result.status == AccountStatus.SUCCESS:
                        batch_result.success_count += 1
                    elif result.status == AccountStatus.FAILED:
                        batch_result.failed_count += 1
                
                return result
                
            except Exception as e:
                logger.error(f"处理配对失败: {e}")
                # 创建失败结果
                pair_id = f"{account.email}_{card.get_last_4_digits()}"
                failed_pair = AccountCardPair(
                    account=account,
                    card=card,
                    pair_id=pair_id,
                    status=AccountStatus.FAILED,
                    error_message=str(e)
                )
                return failed_pair
            
            finally:
                # 清理浏览器上下文
                try:
                    if 'context' in locals():
                        await browser_manager.close_context(context)
                except:
                    pass
    
    async def process_single_pair(self, account: HuluAccount, card: PaymentCard) -> AccountCardPair:
        """处理单个账号-支付卡配对"""
        logger.info(f"开始处理单个配对: {account.email} - ****{card.get_last_4_digits()}")
        
        try:
            result = await self.hulu_processor.process_account_card_pair(account, card)
            logger.info(f"单个配对处理完成: {result.pair_id}, 状态: {result.status}")
            return result
        except Exception as e:
            logger.error(f"单个配对处理失败: {e}")
            pair_id = f"{account.email}_{card.get_last_4_digits()}"
            failed_pair = AccountCardPair(
                account=account,
                card=card,
                pair_id=pair_id,
                status=AccountStatus.FAILED,
                error_message=str(e)
            )
            return failed_pair
    
    def get_batch_status(self, batch_id: str) -> Optional[BatchProcessResult]:
        """获取批次状态"""
        return self.active_batches.get(batch_id)
    
    def get_all_active_batches(self) -> Dict[str, BatchProcessResult]:
        """获取所有活跃批次"""
        return self.active_batches.copy()
    
    def cancel_batch(self, batch_id: str) -> bool:
        """取消批次处理"""
        if batch_id in self.active_batches:
            batch_result = self.active_batches[batch_id]
            batch_result.status = "cancelled"
            batch_result.completed_at = datetime.now()
            
            # 清理信号量
            if batch_id in self.semaphore_pool:
                del self.semaphore_pool[batch_id]
            
            logger.info(f"批次已取消: {batch_id}")
            return True
        
        return False
    
    def cleanup_completed_batches(self, max_age_hours: int = 24):
        """清理已完成的批次"""
        current_time = datetime.now()
        to_remove = []
        
        for batch_id, batch_result in self.active_batches.items():
            if batch_result.completed_at:
                age = current_time - batch_result.completed_at
                if age.total_seconds() > max_age_hours * 3600:
                    to_remove.append(batch_id)
        
        for batch_id in to_remove:
            del self.active_batches[batch_id]
            logger.info(f"清理已完成的批次: {batch_id}")
        
        logger.info(f"清理了 {len(to_remove)} 个已完成的批次")


# 全局批量处理器实例
batch_processor = BatchProcessor()
